/**
 * Custom String Compression Module
 * 
 * This module provides custom string compression functionality that replaces
 * js-confuser's stringCompression option. It uses Babel to find strings and
 * LZ-String for compression, with completely randomized variable names.
 */

const babel = require('@babel/core');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');
const LZString = require('lz-string');
const { generateRandomName } = require('./utils');

/**
 * Generate a completely random and non-descriptive variable name
 * @returns {string} Random variable name
 */
function generateObfuscatedName() {
    return generateRandomName(Math.floor(Math.random() * 8) + 6);
}

/**
 * Check if a string literal is part of a module import/require
 * @param {Object} path - Babel path object
 * @returns {boolean} True if it's a module import
 */
function isModuleImport(path) {
    const parent = path.parent;
    
    // Check for require() calls
    if (t.isCallExpression(parent) && 
        t.isIdentifier(parent.callee, { name: 'require' }) &&
        parent.arguments[0] === path.node) {
        return true;
    }
    
    // Check for import statements
    if (t.isImportDeclaration(path.findParent(p => p.isImportDeclaration())?.node)) {
        return true;
    }
    
    // Check for export statements
    if (t.isExportNamedDeclaration(path.findParent(p => p.isExportNamedDeclaration())?.node) ||
        t.isExportDefaultDeclaration(path.findParent(p => p.isExportDefaultDeclaration())?.node)) {
        return true;
    }
    
    return false;
}

/**
 * Ensure the path can be replaced with a computed expression
 * @param {Object} path - Babel path object
 */
function ensureComputedExpression(path) {
    const parent = path.parent;
    
    // If it's a property key in object notation, convert to computed
    if (t.isObjectProperty(parent) && parent.key === path.node && !parent.computed) {
        parent.computed = true;
    }
    
    // If it's a member expression property, convert to computed
    if (t.isMemberExpression(parent) && parent.property === path.node && !parent.computed) {
        parent.computed = true;
    }
}
// Generate random names for all variables and functions
    const randomNames = {
        r: generateRandomName(),
        o: generateRandomName(),
        n: generateRandomName(),
        e: generateRandomName(),
        t: generateRandomName(),
        i: generateRandomName(),
        s: generateRandomName(),
        u: generateRandomName(),
        a: generateRandomName(),
        p: generateRandomName(),
        c: generateRandomName(),
        l: generateRandomName(),
        f: generateRandomName(),
        h: generateRandomName(),
        d: generateRandomName(),
        m: generateRandomName(),
        v: generateRandomName(),
        g: generateRandomName(),
        
        // Function and method names
        compressToBase64: generateRandomName(),
        decompressFromBase64: generateRandomName(),
        compressToUTF16: generateRandomName(),
        decompressFromUTF16: generateRandomName(),
        compressToUint8Array: generateRandomName(),
        decompressFromUint8Array: generateRandomName(),
        compressToEncodedURIComponent: generateRandomName(),
        decompressFromEncodedURIComponent: generateRandomName(),
        compress: generateRandomName(),
        _compress: generateRandomName(),
        decompress: generateRandomName(),
        _decompress: generateRandomName(),
        
        // Property names
        val: generateRandomName(),
        position: generateRandomName(),
        index: generateRandomName(),
        length: generateRandomName(),
        charAt: generateRandomName(),
        charCodeAt: generateRandomName(),
        hasOwnProperty: generateRandomName(),
        prototype: generateRandomName(),
        join: generateRandomName(),
        push: generateRandomName(),
        replace: generateRandomName(),
        forEach: generateRandomName(),
        call: generateRandomName(),
        pow: generateRandomName(),
        floor: generateRandomName(),
        fromCharCode: generateRandomName()
    };

function generateRandomizedLZStringLibrary() {
    return `
var {StringCompressionLibrary}=function(){var ${randomNames.r}=String["${randomNames.fromCharCode}"],${randomNames.o}="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",${randomNames.n}="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",${randomNames.e}={};function ${randomNames.t}(${randomNames.r},${randomNames.o}){if(!${randomNames.e}[${randomNames.r}]){${randomNames.e}[${randomNames.r}]={};for(var ${randomNames.n}=0;${randomNames.n}<${randomNames.r}["${randomNames.length}"];${randomNames.n}++)${randomNames.e}[${randomNames.r}][${randomNames.r}["${randomNames.charAt}"](${randomNames.n})]=${randomNames.n}}return ${randomNames.e}[${randomNames.r}][${randomNames.o}]}var ${randomNames.i}={"${randomNames.compressToBase64}":function(${randomNames.r}){if(null==${randomNames.r})return"";var ${randomNames.n}=${randomNames.i}["${randomNames._compress}"](${randomNames.r},6,function(${randomNames.r}){return ${randomNames.o}["${randomNames.charAt}"](${randomNames.r})});switch(${randomNames.n}["${randomNames.length}"]%4){default:case 0:return ${randomNames.n};case 1:return ${randomNames.n}+"===";case 2:return ${randomNames.n}+"==";case 3:return ${randomNames.n}+"="}},["${randomNames.decompressFromBase64}"]:function(${randomNames.r}){return null==${randomNames.r}?"":""==${randomNames.r}?null:${randomNames.i}["${randomNames._decompress}"](${randomNames.r}["${randomNames.length}"],32,function(${randomNames.n}){return ${randomNames.t}(${randomNames.o},${randomNames.r}["${randomNames.charAt}"](${randomNames.n}))})},["${randomNames.compressToUTF16}"]:function(${randomNames.o}){return null==${randomNames.o}?"":${randomNames.i}["${randomNames._compress}"](${randomNames.o},15,function(${randomNames.o}){return ${randomNames.r}(${randomNames.o}+32)})+" "},["${randomNames.decompressFromUTF16}"]:function(${randomNames.r}){return null==${randomNames.r}?"":""==${randomNames.r}?null:${randomNames.i}["${randomNames._decompress}"](${randomNames.r}["${randomNames.length}"],16384,function(${randomNames.o}){return ${randomNames.r}["${randomNames.charCodeAt}"](${randomNames.o})-32})},["${randomNames.compressToUint8Array}"]:function(${randomNames.r}){for(var ${randomNames.o}=${randomNames.i}["${randomNames.compress}"](${randomNames.r}),${randomNames.n}=new Uint8Array(2*${randomNames.o}["${randomNames.length}"]),${randomNames.e}=0,${randomNames.t}=${randomNames.o}["${randomNames.length}"];${randomNames.e}<${randomNames.t};${randomNames.e}++){var ${randomNames.s}=${randomNames.o}["${randomNames.charCodeAt}"](${randomNames.e});${randomNames.n}[2*${randomNames.e}]=${randomNames.s}>>>8,${randomNames.n}[2*${randomNames.e}+1]=${randomNames.s}%256}return ${randomNames.n}},["${randomNames.decompressFromUint8Array}"]:function(${randomNames.o}){if(null==${randomNames.o})return ${randomNames.i}["${randomNames.decompress}"](${randomNames.o});for(var ${randomNames.n}=new Array(${randomNames.o}["${randomNames.length}"]/2),${randomNames.e}=0,${randomNames.t}=${randomNames.n}["${randomNames.length}"];${randomNames.e}<${randomNames.t};${randomNames.e}++)${randomNames.n}[${randomNames.e}]=256*${randomNames.o}[2*${randomNames.e}]+${randomNames.o}[2*${randomNames.e}+1];var ${randomNames.s}=[];return ${randomNames.n}["${randomNames.forEach}"](function(${randomNames.o}){${randomNames.s}["${randomNames.push}"](${randomNames.r}(${randomNames.o}))}),${randomNames.i}["${randomNames.decompress}"](${randomNames.s}["${randomNames.join}"](""))},["${randomNames.compressToEncodedURIComponent}"]:function(${randomNames.r}){return null==${randomNames.r}?"":${randomNames.i}["${randomNames._compress}"](${randomNames.r},6,function(${randomNames.r}){return ${randomNames.n}["${randomNames.charAt}"](${randomNames.r})})},["${randomNames.decompressFromEncodedURIComponent}"]:function(${randomNames.r}){return null==${randomNames.r}?"":""==${randomNames.r}?null:(${randomNames.r}=${randomNames.r}["${randomNames.replace}"](/ /g,"+"),${randomNames.i}["${randomNames._decompress}"](${randomNames.r}["${randomNames.length}"],32,function(${randomNames.o}){return ${randomNames.t}(${randomNames.n},${randomNames.r}["${randomNames.charAt}"](${randomNames.o}))}))},["${randomNames.compress}"]:function(${randomNames.o}){return ${randomNames.i}["${randomNames._compress}"](${randomNames.o},16,function(${randomNames.o}){return ${randomNames.r}(${randomNames.o})})},["${randomNames._compress}"]:function(${randomNames.r},${randomNames.o},${randomNames.n}){if(null==${randomNames.r})return"";var ${randomNames.e},${randomNames.t},${randomNames.i},${randomNames.s}={},${randomNames.u}={},${randomNames.a}="",${randomNames.p}="",${randomNames.c}="",${randomNames.l}=2,${randomNames.f}=3,${randomNames.h}=2,${randomNames.d}=[],${randomNames.m}=0,${randomNames.v}=0;for(${randomNames.i}=0;${randomNames.i}<${randomNames.r}["${randomNames.length}"];${randomNames.i}+=1)if(${randomNames.a}=${randomNames.r}["${randomNames.charAt}"](${randomNames.i}),Object["${randomNames.prototype}"]["${randomNames.hasOwnProperty}"]["${randomNames.call}"](${randomNames.s},${randomNames.a})||(${randomNames.s}[${randomNames.a}]=${randomNames.f}++,${randomNames.u}[${randomNames.a}]=!0),${randomNames.p}=${randomNames.c}+${randomNames.a},Object["${randomNames.prototype}"]["${randomNames.hasOwnProperty}"]["${randomNames.call}"](${randomNames.s},${randomNames.p}))${randomNames.c}=${randomNames.p};else{if(Object["${randomNames.prototype}"]["${randomNames.hasOwnProperty}"]["${randomNames.call}"](${randomNames.u},${randomNames.c})){if(${randomNames.c}["${randomNames.charCodeAt}"](0)<256){for(${randomNames.e}=0;${randomNames.e}<${randomNames.h};${randomNames.e}++)${randomNames.m}<<=1,${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++;for(${randomNames.t}=${randomNames.c}["${randomNames.charCodeAt}"](0),${randomNames.e}=0;${randomNames.e}<8;${randomNames.e}++)${randomNames.m}=${randomNames.m}<<1|1&${randomNames.t},${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++,${randomNames.t}>>=1}else{for(${randomNames.t}=1,${randomNames.e}=0;${randomNames.e}<${randomNames.h};${randomNames.e}++)${randomNames.m}=${randomNames.m}<<1|${randomNames.t},${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++,${randomNames.t}=0;for(${randomNames.t}=${randomNames.c}["${randomNames.charCodeAt}"](0),${randomNames.e}=0;${randomNames.e}<16;${randomNames.e}++)${randomNames.m}=${randomNames.m}<<1|1&${randomNames.t},${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++,${randomNames.t}>>=1}0==--${randomNames.l}&&(${randomNames.l}=Math["${randomNames.pow}"](2,${randomNames.h}),${randomNames.h}++),delete ${randomNames.u}[${randomNames.c}]}else for(${randomNames.t}=${randomNames.s}[${randomNames.c}],${randomNames.e}=0;${randomNames.e}<${randomNames.h};${randomNames.e}++)${randomNames.m}=${randomNames.m}<<1|1&${randomNames.t},${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++,${randomNames.t}>>=1;0==--${randomNames.l}&&(${randomNames.l}=Math["${randomNames.pow}"](2,${randomNames.h}),${randomNames.h}++),${randomNames.s}[${randomNames.p}]=${randomNames.f}++,${randomNames.c}=String(${randomNames.a})}if(""!==${randomNames.c}){if(Object["${randomNames.prototype}"]["${randomNames.hasOwnProperty}"]["${randomNames.call}"](${randomNames.u},${randomNames.c})){if(${randomNames.c}["${randomNames.charCodeAt}"](0)<256){for(${randomNames.e}=0;${randomNames.e}<${randomNames.h};${randomNames.e}++)${randomNames.m}<<=1,${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++;for(${randomNames.t}=${randomNames.c}["${randomNames.charCodeAt}"](0),${randomNames.e}=0;${randomNames.e}<8;${randomNames.e}++)${randomNames.m}=${randomNames.m}<<1|1&${randomNames.t},${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++,${randomNames.t}>>=1}else{for(${randomNames.t}=1,${randomNames.e}=0;${randomNames.e}<${randomNames.h};${randomNames.e}++)${randomNames.m}=${randomNames.m}<<1|${randomNames.t},${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++,${randomNames.t}=0;for(${randomNames.t}=${randomNames.c}["${randomNames.charCodeAt}"](0),${randomNames.e}=0;${randomNames.e}<16;${randomNames.e}++)${randomNames.m}=${randomNames.m}<<1|1&${randomNames.t},${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++,${randomNames.t}>>=1}0==--${randomNames.l}&&(${randomNames.l}=Math["${randomNames.pow}"](2,${randomNames.h}),${randomNames.h}++),delete ${randomNames.u}[${randomNames.c}]}else for(${randomNames.t}=${randomNames.s}[${randomNames.c}],${randomNames.e}=0;${randomNames.e}<${randomNames.h};${randomNames.e}++)${randomNames.m}=${randomNames.m}<<1|1&${randomNames.t},${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++,${randomNames.t}>>=1;0==--${randomNames.l}&&(${randomNames.l}=Math["${randomNames.pow}"](2,${randomNames.h}),${randomNames.h}++)}for(${randomNames.t}=2,${randomNames.e}=0;${randomNames.e}<${randomNames.h};${randomNames.e}++)${randomNames.m}=${randomNames.m}<<1|1&${randomNames.t},${randomNames.v}==${randomNames.o}-1?(${randomNames.v}=0,${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m})),${randomNames.m}=0):${randomNames.v}++,${randomNames.t}>>=1;for(;;){if(${randomNames.m}<<=1,${randomNames.v}==${randomNames.o}-1){${randomNames.d}["${randomNames.push}"](${randomNames.n}(${randomNames.m}));break}${randomNames.v}++}return ${randomNames.d}["${randomNames.join}"]("")},["${randomNames.decompress}"]:function(${randomNames.r}){return null==${randomNames.r}?"":""==${randomNames.r}?null:${randomNames.i}["${randomNames._decompress}"](${randomNames.r}["${randomNames.length}"],32768,function(${randomNames.o}){return ${randomNames.r}["${randomNames.charCodeAt}"](${randomNames.o})})},["${randomNames._decompress}"]:function(${randomNames.o},${randomNames.n},${randomNames.e}){var ${randomNames.t},${randomNames.i},${randomNames.s},${randomNames.u},${randomNames.a},${randomNames.p},${randomNames.c},${randomNames.l}=[],${randomNames.f}=4,${randomNames.h}=4,${randomNames.d}=3,${randomNames.m}="",${randomNames.v}=[],${randomNames.g}={"${randomNames.val}":${randomNames.e}(0),"${randomNames.position}":${randomNames.n},"${randomNames.index}":1};for(${randomNames.t}=0;${randomNames.t}<3;${randomNames.t}+=1)${randomNames.l}[${randomNames.t}]=${randomNames.t};for(${randomNames.s}=0,${randomNames.a}=Math["${randomNames.pow}"](2,2),${randomNames.p}=1;${randomNames.p}!=${randomNames.a};)${randomNames.u}=${randomNames.g}["${randomNames.val}"]&${randomNames.g}["${randomNames.position}"],${randomNames.g}["${randomNames.position}"]>>=1,0==${randomNames.g}["${randomNames.position}"]&&(${randomNames.g}["${randomNames.position}"]=${randomNames.n},${randomNames.g}["${randomNames.val}"]=${randomNames.e}(${randomNames.g}["${randomNames.index}"]++)),${randomNames.s}|=(${randomNames.u}>0?1:0)*${randomNames.p},${randomNames.p}<<=1;switch(${randomNames.s}){case 0:for(${randomNames.s}=0,${randomNames.a}=Math["${randomNames.pow}"](2,8),${randomNames.p}=1;${randomNames.p}!=${randomNames.a};)${randomNames.u}=${randomNames.g}["${randomNames.val}"]&${randomNames.g}["${randomNames.position}"],${randomNames.g}["${randomNames.position}"]>>=1,0==${randomNames.g}["${randomNames.position}"]&&(${randomNames.g}["${randomNames.position}"]=${randomNames.n},${randomNames.g}["${randomNames.val}"]=${randomNames.e}(${randomNames.g}["${randomNames.index}"]++)),${randomNames.s}|=(${randomNames.u}>0?1:0)*${randomNames.p},${randomNames.p}<<=1;${randomNames.c}=${randomNames.r}(${randomNames.s});break;case 1:for(${randomNames.s}=0,${randomNames.a}=Math["${randomNames.pow}"](2,16),${randomNames.p}=1;${randomNames.p}!=${randomNames.a};)${randomNames.u}=${randomNames.g}["${randomNames.val}"]&${randomNames.g}["${randomNames.position}"],${randomNames.g}["${randomNames.position}"]>>=1,0==${randomNames.g}["${randomNames.position}"]&&(${randomNames.g}["${randomNames.position}"]=${randomNames.n},${randomNames.g}["${randomNames.val}"]=${randomNames.e}(${randomNames.g}["${randomNames.index}"]++)),${randomNames.s}|=(${randomNames.u}>0?1:0)*${randomNames.p},${randomNames.p}<<=1;${randomNames.c}=${randomNames.r}(${randomNames.s});break;case 2:return""}for(${randomNames.l}[3]=${randomNames.c},${randomNames.i}=${randomNames.c},${randomNames.v}["${randomNames.push}"](${randomNames.c});;){if(${randomNames.g}["${randomNames.index}"]>${randomNames.o})return"";for(${randomNames.s}=0,${randomNames.a}=Math["${randomNames.pow}"](2,${randomNames.d}),${randomNames.p}=1;${randomNames.p}!=${randomNames.a};)${randomNames.u}=${randomNames.g}["${randomNames.val}"]&${randomNames.g}["${randomNames.position}"],${randomNames.g}["${randomNames.position}"]>>=1,0==${randomNames.g}["${randomNames.position}"]&&(${randomNames.g}["${randomNames.position}"]=${randomNames.n},${randomNames.g}["${randomNames.val}"]=${randomNames.e}(${randomNames.g}["${randomNames.index}"]++)),${randomNames.s}|=(${randomNames.u}>0?1:0)*${randomNames.p},${randomNames.p}<<=1;switch(${randomNames.c}=${randomNames.s}){case 0:for(${randomNames.s}=0,${randomNames.a}=Math["${randomNames.pow}"](2,8),${randomNames.p}=1;${randomNames.p}!=${randomNames.a};)${randomNames.u}=${randomNames.g}["${randomNames.val}"]&${randomNames.g}["${randomNames.position}"],${randomNames.g}["${randomNames.position}"]>>=1,0==${randomNames.g}["${randomNames.position}"]&&(${randomNames.g}["${randomNames.position}"]=${randomNames.n},${randomNames.g}["${randomNames.val}"]=${randomNames.e}(${randomNames.g}["${randomNames.index}"]++)),${randomNames.s}|=(${randomNames.u}>0?1:0)*${randomNames.p},${randomNames.p}<<=1;${randomNames.l}[${randomNames.h}++]=${randomNames.r}(${randomNames.s}),${randomNames.c}=${randomNames.h}-1,${randomNames.f}--;break;case 1:for(${randomNames.s}=0,${randomNames.a}=Math["${randomNames.pow}"](2,16),${randomNames.p}=1;${randomNames.p}!=${randomNames.a};)${randomNames.u}=${randomNames.g}["${randomNames.val}"]&${randomNames.g}["${randomNames.position}"],${randomNames.g}["${randomNames.position}"]>>=1,0==${randomNames.g}["${randomNames.position}"]&&(${randomNames.g}["${randomNames.position}"]=${randomNames.n},${randomNames.g}["${randomNames.val}"]=${randomNames.e}(${randomNames.g}["${randomNames.index}"]++)),${randomNames.s}|=(${randomNames.u}>0?1:0)*${randomNames.p},${randomNames.p}<<=1;${randomNames.l}[${randomNames.h}++]=${randomNames.r}(${randomNames.s}),${randomNames.c}=${randomNames.h}-1,${randomNames.f}--;break;case 2:return ${randomNames.v}["${randomNames.join}"]("")}if(0==${randomNames.f}&&(${randomNames.f}=Math["${randomNames.pow}"](2,${randomNames.d}),${randomNames.d}++),${randomNames.l}[${randomNames.c}])${randomNames.m}=${randomNames.l}[${randomNames.c}];else{if(${randomNames.c}!==${randomNames.h})return null;${randomNames.m}=${randomNames.i}+${randomNames.i}["${randomNames.charAt}"](0)}${randomNames.v}["${randomNames.push}"](${randomNames.m}),${randomNames.l}[${randomNames.h}++]=${randomNames.i}+${randomNames.m}["${randomNames.charAt}"](0),${randomNames.i}=${randomNames.m},0==--${randomNames.f}&&(${randomNames.f}=Math["${randomNames.pow}"](2,${randomNames.d}),${randomNames.d}++)}}};return ${randomNames.i}}();"function"==typeof define&&define.amd?define(function(){return {StringCompressionLibrary}}):"undefined"!=typeof module&&null!=module?module.exports={StringCompressionLibrary}:"undefined"!=typeof angular&&null!=angular&&angular.module("LZString",[]).factory("LZString",function(){return {StringCompressionLibrary}});`;
}

/**
 * LZ-String library minified code with randomized variable names
 */
const StringCompressionLibraryMinified = generateRandomizedLZStringLibrary();

/**
 * Create the string decompression template
 * @param {string} functionName - Name of the decompression function
 * @param {string} compressedString - The compressed string data
 * @param {string} stringDelimiter - Delimiter used to separate strings
 * @returns {string} Decompression code template
 */
function createDecompressionTemplate(functionName, compressedString, stringDelimiter, libraryVarName) {
    const arrayVarName = generateObfuscatedName();
    const tempVarName = generateObfuscatedName();
    const indexVarName = generateObfuscatedName();
    const decompressVarName = generateObfuscatedName();

    return `
var ${functionName};
(function() {
    var ${decompressVarName} = "${compressedString}";
    var ${tempVarName} = ${libraryVarName};
    var ${arrayVarName} = ${tempVarName}["${randomNames.decompressFromUTF16}"](${decompressVarName})["split"]("${stringDelimiter}");
    ${functionName} = function(${indexVarName}) {
        return ${arrayVarName}[${indexVarName}];
    };
})();`;
}



/**
 * Process JavaScript code to compress strings
 * @param {string} code - The JavaScript code to process
 * @param {Object} options - Processing options
 * @returns {Object} Result object with processed code and metadata
 */
async function processStringCompression(code, options = {}) {
    try {
        const startTime = Date.now();
        const stringDelimiter = "|";
        const stringMap = new Map();
        let stringsProcessed = 0;

        // Generate single random function name for all string decompression
        const mainDecompressFnName = generateObfuscatedName();

        // Parse the code into AST
        const ast = parser.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: ['jsx', 'typescript', 'decorators-legacy']
        });

        // Traverse AST to find and collect strings
        traverse(ast, {
            StringLiteral(path) {
                // Skip module imports
                if (isModuleImport(path)) {
                    return;
                }

                const originalValue = path.node.value;

                // Must be at least 2 characters long
                if (originalValue.length < 2) {
                    return;
                }

                // Cannot contain the string delimiter
                if (originalValue.includes(stringDelimiter)) {
                    return;
                }

                // Skip very short or single character strings
                if (originalValue.trim().length < 1) {
                    return;
                }

                let index = stringMap.get(originalValue);
                if (typeof index === 'undefined') {
                    index = stringMap.size;
                    stringMap.set(originalValue, index);
                }

                stringsProcessed++;

                // Ensure the string can be replaced with a computed expression
                ensureComputedExpression(path);

                // Replace the string literal with a call to the main decompression function
                path.replaceWith(
                    t.callExpression(
                        t.identifier(mainDecompressFnName),
                        [t.numericLiteral(index)]
                    )
                );
            }
        });
        
        // If no strings were processed, return original code
        if (stringMap.size === 0) {
            return {
                code: code,
                success: true,
                processingTime: Date.now() - startTime,
                stringsCompressed: 0,
                compressionRatio: 1.0,
                originalSize: Buffer.byteLength(code, 'utf8'),
                compressedSize: Buffer.byteLength(code, 'utf8')
            };
        }
        
        // Create string payload and compress it
        const stringPayload = Array.from(stringMap.keys()).join(stringDelimiter);
        const compressedString = LZString.compressToUTF16(stringPayload);
        
        // Generate random library variable name
        const stringCompressionLibraryName = generateObfuscatedName();

        // Create the LZ-String library injection with randomized name
        const lzStringLibraryCode = StringCompressionLibraryMinified.replace(
            /{StringCompressionLibrary}/g,
            stringCompressionLibraryName
        );

        // Create decompression template
        const decompressionCode = createDecompressionTemplate(
            mainDecompressFnName,
            compressedString,
            stringDelimiter,
            stringCompressionLibraryName
        );

        // Generate the final code
        const generatedCode = generate(ast, {
            compact: false,
            minified: false
        }).code;

        // Combine library, decompression function and processed code
        const finalCode = lzStringLibraryCode + '\n' + decompressionCode + '\n' + generatedCode;
        
        const processingTime = Date.now() - startTime;
        const originalSize = Buffer.byteLength(code, 'utf8');
        const finalSize = Buffer.byteLength(finalCode, 'utf8');
        
        return {
            code: finalCode,
            success: true,
            processingTime: processingTime,
            stringsCompressed: stringMap.size,
            stringsProcessed: stringsProcessed,
            compressionRatio: finalSize / originalSize,
            originalSize: originalSize,
            compressedSize: finalSize,
            metadata: {
                stringDelimiter: stringDelimiter,
                compressedStringLength: compressedString.length,
                originalStringPayloadLength: stringPayload.length,
                uniqueStrings: stringMap.size
            }
        };
        
    } catch (error) {
        console.error('String compression processing error:', error);
        
        return {
            code: code, // Return original code on failure
            success: false,
            error: error.message,
            processingTime: 0,
            stringsCompressed: 0,
            compressionRatio: 1.0,
            originalSize: Buffer.byteLength(code, 'utf8'),
            compressedSize: Buffer.byteLength(code, 'utf8')
        };
    }
}

module.exports = {
    processStringCompression,
    generateObfuscatedName,
    createDecompressionTemplate,
    StringCompressionLibraryMinified
};
